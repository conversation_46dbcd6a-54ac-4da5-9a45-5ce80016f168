package me.zivush.cubes;


import org.bukkit.ChatColor;
import org.bukkit.Location;
import org.bukkit.Material;
import org.bukkit.block.Block;
import org.bukkit.entity.Player;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;
import org.bukkit.inventory.meta.SkullMeta;
import org.bukkit.scheduler.BukkitRunnable;

import java.io.File;
import java.lang.reflect.Field;
import java.util.*;
import java.util.concurrent.ThreadLocalRandom;

public class CubeManager {
    private final Cubes plugin;
    private final ConfigManager configManager;
    private final Map<String, CubeData> activeCubes;
    private final Map<Location, String> cubeLocations;
    private final Map<String, BukkitRunnable> regenerationTasks;
    
    public CubeManager(Cubes plugin, ConfigManager configManager) {
        this.plugin = plugin;
        this.configManager = configManager;
        this.activeCubes = new HashMap<>();
        this.cubeLocations = new HashMap<>();
        this.regenerationTasks = new HashMap<>();
        loadExistingCubes();
    }
    
    private void loadExistingCubes() {
        File dataFolder = new File(plugin.getDataFolder(), "data");
        if (!dataFolder.exists()) return;
        
        File[] files = dataFolder.listFiles((dir, name) -> name.endsWith(".yml"));
        if (files != null) {
            for (File file : files) {
                String cubeId = file.getName().replace(".yml", "");
                CubeData cubeData = new CubeData(cubeId);
                activeCubes.put(cubeId, cubeData);
                cubeLocations.put(cubeData.getLocation(), cubeId);
                startRegeneration(cubeData);
            }
        }
    }
    
    public ItemStack createCubeItem(String cubeType) {
        String materialName = configManager.getConfig().getString("cubes." + cubeType + ".item.material", "PLAYER_HEAD");
        Material material = Material.valueOf(materialName.toUpperCase());
        
        ItemStack item = new ItemStack(material);
        ItemMeta meta = item.getItemMeta();
        
        if (meta != null) {
            String name = configManager.getConfig().getString("cubes." + cubeType + ".item.name", "&6" + cubeType + " Cube");
            meta.setDisplayName(ChatColor.translateAlternateColorCodes('&', name));
            
            List<String> lore = configManager.getConfig().getStringList("cubes." + cubeType + ".item.lore");
            if (!lore.isEmpty()) {
                List<String> coloredLore = new ArrayList<>();
                for (String line : lore) {
                    coloredLore.add(ChatColor.translateAlternateColorCodes('&', line));
                }
                meta.setLore(coloredLore);
            }
            
            // Handle player head texture using URL/Base64
            if (material == Material.PLAYER_HEAD && meta instanceof SkullMeta) {
                String textureValue = configManager.getConfig().getString("cubes." + cubeType + ".item.head-texture");
                String textureUrl = configManager.getConfig().getString("cubes." + cubeType + ".item.head-url");

                if (textureValue != null && !textureValue.isEmpty()) {
                    setSkullTexture((SkullMeta) meta, textureValue, false);
                } else if (textureUrl != null && !textureUrl.isEmpty()) {
                    setSkullTexture((SkullMeta) meta, textureUrl, true);
                }
            }
            
            item.setItemMeta(meta);
        }
        
        return item;
    }

    private void setSkullTexture(SkullMeta skullMeta, String textureData, boolean isUrl) {
        try {
            // Get the profile field from SkullMeta
            Field profileField = skullMeta.getClass().getDeclaredField("profile");
            profileField.setAccessible(true);

            // Create a new GameProfile using reflection
            Class<?> gameProfileClass = Class.forName("com.mojang.authlib.GameProfile");
            Class<?> propertyClass = Class.forName("com.mojang.authlib.properties.Property");

            Object gameProfile = gameProfileClass.getConstructor(UUID.class, String.class)
                    .newInstance(UUID.randomUUID(), null);

            // Get the properties map
            Object properties = gameProfileClass.getMethod("getProperties").invoke(gameProfile);

            String textureValue;
            if (isUrl) {
                // Convert URL to Base64 texture value
                String textureJson = "{\"textures\":{\"SKIN\":{\"url\":\"" + textureData + "\"}}}";
                textureValue = java.util.Base64.getEncoder().encodeToString(textureJson.getBytes());
            } else {
                textureValue = textureData;
            }

            // Create Property object
            Object property = propertyClass.getConstructor(String.class, String.class)
                    .newInstance("textures", textureValue);

            // Add property to the profile
            properties.getClass().getMethod("put", Object.class, Object.class)
                    .invoke(properties, "textures", property);

            // Set the profile to the skull meta
            profileField.set(skullMeta, gameProfile);

        } catch (Exception e) {
            // If reflection fails, fall back to a default head
            getLogger().warning("Failed to set custom skull texture: " + e.getMessage());
            skullMeta.setOwner("MHF_Question");
        }
    }

    private java.util.logging.Logger getLogger() {
        return plugin.getLogger();
    }

    public boolean placeCube(Player player, Location location, String cubeType) {
        int size = configManager.getCubeSize(cubeType);

        // Calculate the actual cube origin based on player's facing direction
        Location cubeOrigin = calculateCubeOrigin(location, size, player);

        // Check if there's enough space at the calculated origin
        if (!hasEnoughSpace(cubeOrigin, size)) {
            player.sendMessage(configManager.getMessage("messages.not-enough-space"));
            return false;
        }

        // Create cube ID
        String cubeId = UUID.randomUUID().toString();

        // Create cube data with the calculated origin
        CubeData cubeData = new CubeData(cubeId, player.getUniqueId(), cubeType, cubeOrigin);
        activeCubes.put(cubeId, cubeData);
        cubeLocations.put(cubeOrigin, cubeId);

        // Build the cube
        buildCube(cubeData);

        // Start regeneration
        startRegeneration(cubeData);

        player.sendMessage(configManager.getMessage("messages.cube-placed"));
        return true;
    }

    private Location calculateCubeOrigin(Location clickedLocation, int size, Player player) {
        // The clicked block is BELOW the foundation
        // The block 1 ABOVE the clicked block should be the CENTER of the foundation
        // For a 5x5 cube: the block above clicked should be at position (2, 0, 2) relative to cube origin
        // So cube origin should be at (clicked location + 1Y) - (size/2, 0, size/2)

        int halfSize = size / 2;

        // Calculate offset so the block ABOVE clicked becomes center of foundation
        int offsetX = -halfSize;
        int offsetZ = -halfSize;
        int offsetY = 1; // Foundation is 1 block ABOVE the clicked block

        // Return the calculated origin location
        return clickedLocation.clone().add(offsetX, offsetY, offsetZ);
    }

    private boolean hasEnoughSpace(Location location, int size) {
        for (int x = -1; x <= size; x++) {
            for (int y = 0; y <= size + 1; y++) {
                for (int z = -1; z <= size; z++) {
                    Block block = location.clone().add(x, y, z).getBlock();
                    if (block.getType() != Material.AIR) {
                        return false;
                    }
                }
            }
        }
        return true;
    }
    
    private void buildCube(CubeData cubeData) {
        String cubeType = cubeData.getCubeType();
        int size = configManager.getCubeSize(cubeType);
        Material borderMaterial = configManager.getBorderMaterial(cubeType);
        Map<Material, Double> materials = configManager.getCubeMaterials(cubeType);
        
        Location loc = cubeData.getLocation();
        
        // Build borders (full bottom, outline top and sides)
        for (int x = -1; x <= size; x++) {
            for (int y = 0; y <= size + 1; y++) {
                for (int z = -1; z <= size; z++) {
                    // Check if this position is on the border
                    boolean isBorder = false;

                    // Bottom face (FULL coverage - foundation)
                    if (y == 0) {
                        isBorder = true;
                    }
                    // Top face (outline only)
                    else if (y == size + 1) {
                        if (x == -1 || x == size || z == -1 || z == size) {
                            isBorder = true;
                        }
                    }
                    // Vertical edges (corners only)
                    else if ((x == -1 || x == size) && (z == -1 || z == size)) {
                        isBorder = true;
                    }

                    if (isBorder) {
                        loc.clone().add(x, y, z).getBlock().setType(borderMaterial);
                    }
                }
            }
        }
        
        // Fill inside with materials
        fillCubeWithMaterials(cubeData, materials);
    }
    
    private void fillCubeWithMaterials(CubeData cubeData, Map<Material, Double> materials) {
        String cubeType = cubeData.getCubeType();
        int size = configManager.getCubeSize(cubeType);
        Location loc = cubeData.getLocation();

        int totalBlocks = size * size * size;
        List<Material> materialList = new ArrayList<>();

        // Calculate exact block counts for each material
        int assignedBlocks = 0;
        for (Map.Entry<Material, Double> entry : materials.entrySet()) {
            int count = (int) Math.round(entry.getValue() / 100.0 * totalBlocks);
            for (int i = 0; i < count; i++) {
                materialList.add(entry.getKey());
            }
            assignedBlocks += count;
        }

        // Fill any remaining blocks with the most common material
        Material mostCommonMaterial = materials.entrySet().stream()
            .max(Map.Entry.comparingByValue())
            .map(Map.Entry::getKey)
            .orElse(Material.STONE);

        while (materialList.size() < totalBlocks) {
            materialList.add(mostCommonMaterial);
        }

        // Remove excess blocks if we have too many
        while (materialList.size() > totalBlocks) {
            materialList.remove(materialList.size() - 1);
        }

        Collections.shuffle(materialList);

        int index = 0;
        for (int x = 0; x < size; x++) {
            for (int y = 1; y <= size; y++) {
                for (int z = 0; z < size; z++) {
                    if (index < materialList.size()) {
                        loc.clone().add(x, y, z).getBlock().setType(materialList.get(index));
                        index++;
                    }
                }
            }
        }
    }
    
    public void upgradeCube(String cubeId, String newCubeType) {
        CubeData cubeData = activeCubes.get(cubeId);
        if (cubeData == null) return;

        // Stop current regeneration
        BukkitRunnable task = regenerationTasks.get(cubeId);
        if (task != null) {
            task.cancel();
            regenerationTasks.remove(cubeId);
        }

        // Clear current cube blocks
        clearCubeBlocks(cubeData);

        // Create new cube data with upgraded type
        CubeData newCubeData = new CubeData(cubeId, cubeData.getOwner(), newCubeType, cubeData.getLocation());

        // Transfer inventory
        for (Map.Entry<Material, Integer> entry : cubeData.getInventory().entrySet()) {
            newCubeData.addToInventory(entry.getKey(), entry.getValue());
        }

        // Update active cubes
        activeCubes.put(cubeId, newCubeData);

        // Build new cube
        buildCube(newCubeData);

        // Start new regeneration
        startRegeneration(newCubeData);

        // Delete old data and save new
        cubeData.delete();
        newCubeData.saveData();
    }

    public void rebuildCube(String cubeId) {
        CubeData cubeData = activeCubes.get(cubeId);
        if (cubeData == null) return;

        String cubeType = cubeData.getCubeType();
        Map<Material, Double> materials = configManager.getCubeMaterials(cubeType);

        // Fill all missing blocks inside the cube
        fillCubeWithMaterials(cubeData, materials);
    }

    public void removeCube(String cubeId) {
        CubeData cubeData = activeCubes.get(cubeId);
        if (cubeData == null) return;

        // Stop regeneration
        BukkitRunnable task = regenerationTasks.get(cubeId);
        if (task != null) {
            task.cancel();
            regenerationTasks.remove(cubeId);
        }

        // Clear blocks
        clearCubeBlocks(cubeData);

        // Remove data
        cubeData.delete();
        activeCubes.remove(cubeId);
        cubeLocations.remove(cubeData.getLocation());
    }
    
    private void clearCubeBlocks(CubeData cubeData) {
        String cubeType = cubeData.getCubeType();
        int size = configManager.getCubeSize(cubeType);
        Location loc = cubeData.getLocation();
        
        for (int x = -1; x <= size; x++) {
            for (int y = 0; y <= size + 1; y++) {
                for (int z = -1; z <= size; z++) {
                    loc.clone().add(x, y, z).getBlock().setType(Material.AIR);
                }
            }
        }
    }
    
    private void startRegeneration(CubeData cubeData) {
        String cubeType = cubeData.getCubeType();
        if (!configManager.hasRegeneration(cubeType)) return;
        
        int timing = configManager.getRegenerationTiming(cubeType);
        
        BukkitRunnable task = new BukkitRunnable() {
            @Override
            public void run() {
                regenerateBlocks(cubeData);
            }
        };
        
        task.runTaskTimer(plugin, timing, timing);
        regenerationTasks.put(cubeData.getCubeId(), task);
    }
    
    private void regenerateBlocks(CubeData cubeData) {
        String cubeType = cubeData.getCubeType();
        double quantity = configManager.getRegenerationQuantity(cubeType);
        String type = configManager.getRegenerationType(cubeType);
        Map<Material, Double> materials = configManager.getCubeMaterials(cubeType);
        
        int size = configManager.getCubeSize(cubeType);
        Location loc = cubeData.getLocation();
        
        List<Location> airBlocks = new ArrayList<>();
        for (int x = 0; x < size; x++) {
            for (int y = 1; y <= size; y++) {
                for (int z = 0; z < size; z++) {
                    Location blockLoc = loc.clone().add(x, y, z);
                    if (blockLoc.getBlock().getType() == Material.AIR) {
                        airBlocks.add(blockLoc);
                    }
                }
            }
        }
        
        if (airBlocks.isEmpty()) return;
        
        int blocksToRegenerate = (int) (airBlocks.size() * quantity / 100.0);
        
        if (type.equals("RANDOM")) {
            Collections.shuffle(airBlocks);
        }
        
        for (int i = 0; i < Math.min(blocksToRegenerate, airBlocks.size()); i++) {
            Material material = getRandomMaterial(materials);
            airBlocks.get(i).getBlock().setType(material);
        }
    }
    
    private Material getRandomMaterial(Map<Material, Double> materials) {
        double totalWeight = materials.values().stream().mapToDouble(Double::doubleValue).sum();
        double random = ThreadLocalRandom.current().nextDouble() * totalWeight;
        
        double currentWeight = 0;
        for (Map.Entry<Material, Double> entry : materials.entrySet()) {
            currentWeight += entry.getValue();
            if (random <= currentWeight) {
                return entry.getKey();
            }
        }
        
        return materials.keySet().iterator().next();
    }
    
    // Getters
    public CubeData getCubeData(String cubeId) { return activeCubes.get(cubeId); }
    public String getCubeIdAtLocation(Location location) { return cubeLocations.get(location); }
    public Map<String, CubeData> getActiveCubes() { return new HashMap<>(activeCubes); }
}
